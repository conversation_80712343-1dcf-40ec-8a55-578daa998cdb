-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1:3306
-- <PERSON><PERSON><PERSON><PERSON> le : mer. 28 mai 2025 à 18:34
-- Version du serveur : 9.1.0
-- Version de PHP : 8.1.31

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `nuitblanche`
--

-- --------------------------------------------------------

--
-- Structure de la table `addresses`
--

DROP TABLE IF EXISTS `addresses`;
CREATE TABLE IF NOT EXISTS `addresses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `address_line1` varchar(100) NOT NULL,
  `address_line2` varchar(100) DEFAULT NULL,
  `city` varchar(50) NOT NULL,
  `postal_code` varchar(20) NOT NULL,
  `country` varchar(50) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `is_default_shipping` tinyint(1) DEFAULT '0',
  `is_default_billing` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `addresses`
--

INSERT INTO `addresses` (`id`, `user_id`, `address_line1`, `address_line2`, `city`, `postal_code`, `country`, `phone`, `is_default_shipping`, `is_default_billing`, `created_at`, `updated_at`) VALUES
(1, 4, 'Fedsfdssc  fesf fdez', '', 'test', '21014', 'France', '12121212', 1, 1, '2025-05-28 17:06:34', '2025-05-28 17:06:34');

-- --------------------------------------------------------

--
-- Structure de la table `cart_items`
--

DROP TABLE IF EXISTS `cart_items`;
CREATE TABLE IF NOT EXISTS `cart_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `product_id` int NOT NULL,
  `quantity` int NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_product` (`user_id`,`product_id`),
  KEY `product_id` (`product_id`)
) ENGINE=MyISAM AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `cart_items`
--

INSERT INTO `cart_items` (`id`, `user_id`, `product_id`, `quantity`, `created_at`, `updated_at`) VALUES
(3, 1, 1, 1, '2025-05-24 14:49:35', '2025-05-24 14:49:35'),
(4, 1, 2, 1, '2025-05-24 14:49:35', '2025-05-24 14:49:35'),
(5, 1, 3, 1, '2025-05-24 14:49:35', '2025-05-24 14:49:35');

-- --------------------------------------------------------

--
-- Structure de la table `categories`
--

DROP TABLE IF EXISTS `categories`;
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `slug` varchar(50) NOT NULL,
  `description` text,
  `parent_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `parent_id` (`parent_id`)
) ENGINE=MyISAM AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `categories`
--

INSERT INTO `categories` (`id`, `name`, `slug`, `description`, `parent_id`, `created_at`, `updated_at`) VALUES
(1, 'Homme', 'homme', 'Collection pour homme', NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26'),
(2, 'Femme', 'femme', 'Collection pour femme', NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26'),
(3, 'Vêtements', 'vetements-homme', 'Vêtements pour homme', 1, '2025-05-24 14:00:26', '2025-05-28 18:29:47'),
(4, 'Accessoires', 'accessoires-homme', 'Accessoires pour homme', 1, '2025-05-24 14:00:26', '2025-05-28 18:29:08'),
(5, 'Parfums', 'parfums-homme', 'Parfums pour homme', 1, '2025-05-24 14:00:26', '2025-05-28 18:29:39'),
(6, 'Vêtements', 'vetements-femme', 'Vêtements pour femme', 2, '2025-05-24 14:00:26', '2025-05-28 18:29:58'),
(7, 'Accessoires', 'accessoires-femme', 'Accessoires pour femme', 2, '2025-05-24 14:00:26', '2025-05-28 18:29:26'),
(8, 'Parfums', 'parfums-femme', 'Parfums pour femme', 2, '2025-05-24 14:00:26', '2025-05-28 18:29:53');

-- --------------------------------------------------------

--
-- Structure de la table `orders`
--

DROP TABLE IF EXISTS `orders`;
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `order_number` varchar(20) NOT NULL,
  `status` enum('pending','processing','shipped','delivered','cancelled') DEFAULT 'pending',
  `total_amount` decimal(10,2) NOT NULL,
  `shipping_address_id` int NOT NULL,
  `billing_address_id` int NOT NULL,
  `shipping_method` varchar(50) DEFAULT NULL,
  `shipping_cost` decimal(10,2) DEFAULT '0.00',
  `payment_method` varchar(50) DEFAULT NULL,
  `notes` text,
  `livreur_id` int DEFAULT NULL,
  `delivered_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `user_id` (`user_id`),
  KEY `shipping_address_id` (`shipping_address_id`),
  KEY `billing_address_id` (`billing_address_id`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `orders`
--

INSERT INTO `orders` (`id`, `user_id`, `order_number`, `status`, `total_amount`, `shipping_address_id`, `billing_address_id`, `shipping_method`, `shipping_cost`, `payment_method`, `notes`, `livreur_id`, `delivered_at`, `created_at`, `updated_at`) VALUES
(1, 4, 'NB202505288460', 'delivered', 100.00, 1, 1, 'standard', 15.00, 'card', NULL, 5, '2025-05-28 16:44:46', '2025-05-28 17:22:11', '2025-05-28 17:44:46'),
(2, 4, 'NB202505286667', 'delivered', 54.09, 1, 1, 'standard', 15.00, 'card', NULL, 5, '2025-05-28 16:45:04', '2025-05-28 17:44:39', '2025-05-28 17:45:04');

-- --------------------------------------------------------

--
-- Structure de la table `order_items`
--

DROP TABLE IF EXISTS `order_items`;
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL,
  `product_id` int NOT NULL,
  `quantity` int NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `product_id` (`product_id`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `order_items`
--

INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `price`, `created_at`) VALUES
(1, 1, 10, 1, 85.00, '2025-05-28 17:22:11'),
(2, 2, 8, 1, 39.09, '2025-05-28 17:44:39');

-- --------------------------------------------------------

--
-- Structure de la table `products`
--

DROP TABLE IF EXISTS `products`;
CREATE TABLE IF NOT EXISTS `products` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL,
  `sale_price` decimal(10,2) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `category_id` int DEFAULT NULL,
  `stock` int NOT NULL DEFAULT '0',
  `is_featured` tinyint(1) DEFAULT '0',
  `ImageFile` longblob,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `category_id` (`category_id`)
) ENGINE=MyISAM AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `products`
--

INSERT INTO `products` (`id`, `name`, `slug`, `description`, `price`, `sale_price`, `image`, `category_id`, `stock`, `is_featured`, `ImageFile`, `created_at`, `updated_at`) VALUES
(1, 'Polo rustique beige clair', 'polo-rustique-beige-clair', 'Polo naturel en coton à la fois élégant et respirant', 175.00, 140.00, 'images/products/chemise nuit blanche.jpeg', 3, 25, 1, NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26'),
(2, 'Chemise en laine baby fit', 'chemise-laine-baby-fit', 'Coupe en laine légère parfaite en restaurant contemporain', 219.99, NULL, 'images/products/chesmisesBlanches.jpeg', 3, 15, 1, NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26'),
(3, 'Lunettes de soleil en blanc cassé', 'lunettes-soleil-blanc-casse', 'Monture translucide et verres polarisés', 109.99, 93.49, 'images/products/lunettes nuit blanche.jpg', 4, 30, 0, NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26'),
(4, 'Chapeau de plage', 'chapeau-plage', 'Pour des journées ensoleillées avec style', 49.99, 37.49, 'images/products/LUNETES.jpg', 4, 40, 0, NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26'),
(5, 'Maillot de bain', 'maillot-bain', 'Coupe minimaliste avec poche zip discrète', 59.99, 53.99, 'images/products/model nuit blanche.jpeg', 3, 20, 0, NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26'),
(6, 'Pantalons Slack', 'pantalons-slack', 'Coupe pour une fluidité et un confort optimal', 98.99, 79.19, 'images/products/alex consani nuit blanche.jpg', 3, 18, 0, NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26'),
(7, 'Tote bag', 'tote-bag', 'Essentiel pour les jours en plein air', 19.99, NULL, 'images/products/tote bag.jpg', 7, 50, 0, NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26'),
(8, 'Claquettes', 'claquettes', 'Confortables et stylés en même temps', 45.99, 39.09, 'images/products/claquettes.jpg', 4, 34, 0, NULL, '2025-05-24 14:00:26', '2025-05-28 17:44:39'),
(9, 'Bikini blanc simple', 'bikini-blanc-simple', 'Le fils amount of minimalism pensé pour sublimer toutes les carnations', 85.99, NULL, 'images/products/robe nuit blanche.jpeg', 6, 22, 1, NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26'),
(10, 'Parfum Blanche', 'parfum-blanche', 'Une fragrance pure et lumineuse aux notes de fleur d\'oranger, de jasmin et de musc blanc.', 85.00, NULL, 'images/products/fabric frame nuit blanche.jpg', 8, 29, 1, NULL, '2025-05-24 14:00:26', '2025-05-28 17:22:11'),
(11, 'Parfum Nocturne', 'parfum-nocturne', 'Une fragrance mystérieuse et envoûtante aux notes de bois de santal, de vanille et d\'ambre.', 85.00, NULL, 'images/products/paysage nuit blanche.jpeg', 5, 30, 0, NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26'),
(12, 'Parfum Silence', 'parfum-silence', 'Une fragrance apaisante et délicate aux notes de lavande, de bergamote et de cèdre.', 85.00, NULL, 'images/products/sac nuit blanche.jpeg', 5, 30, 0, NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26'),
(13, 'Trio Essentiel', 'trio-essentiel', 'Découvrez nos trois parfums emblématiques dans un coffret élégant. Chaque flacon contient 15ml de parfum.', 95.00, NULL, 'images/products/soldes.jpg', 8, 15, 1, NULL, '2025-05-24 14:00:26', '2025-05-24 14:00:26');

-- --------------------------------------------------------

--
-- Structure de la table `product_images`
--

DROP TABLE IF EXISTS `product_images`;
CREATE TABLE IF NOT EXISTS `product_images` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `is_primary` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Structure de la table `addresses`
--

DROP TABLE IF EXISTS `addresses`;
CREATE TABLE IF NOT EXISTS `addresses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `type` enum('shipping','billing') DEFAULT 'shipping',
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `address_line1` varchar(255) NOT NULL,
  `address_line2` varchar(255) DEFAULT NULL,
  `city` varchar(100) NOT NULL,
  `postal_code` varchar(20) NOT NULL,
  `country` varchar(100) DEFAULT 'France',
  `phone` varchar(20) DEFAULT NULL,
  `is_default` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `addresses`
--

INSERT INTO `addresses` (`id`, `user_id`, `type`, `first_name`, `last_name`, `address_line1`, `address_line2`, `city`, `postal_code`, `country`, `phone`, `is_default`, `created_at`, `updated_at`) VALUES
(1, 4, 'shipping', 'Chedly', 'Hachicha', '123 Rue de la Paix', NULL, 'Tunis', '1000', 'Tunisie', NULL, 1, '2025-05-28 17:00:00', '2025-05-28 17:00:00');

-- --------------------------------------------------------

--
-- Structure de la table `users`
--

DROP TABLE IF EXISTS `users`;
CREATE TABLE IF NOT EXISTS `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `birth_date` date DEFAULT NULL,
  `role` enum('"USER"','"ADMIN"','"LIVREUR"') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '"USER"',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `users`
--

INSERT INTO `users` (`id`, `first_name`, `last_name`, `email`, `password`, `phone`, `birth_date`, `role`, `created_at`, `updated_at`) VALUES
(1, 'Hedi', 'Souissi', '<EMAIL>', '$2y$10$P39F6zMLSKZhd.zwIJX3D.fx9rEn6UBRPHsXD/zKFy/zI9IwxGrnS', NULL, NULL, '\"USER\"', '2025-05-24 12:16:22', '2025-05-24 12:16:22'),
(5, 'Ferid', 'Hafeni', '<EMAIL>', '$2y$10$y4KC8hvWFoXU548GMfRMme6wqT0m2g5ykp4CKw9fyIXE3PeY8qLr2', NULL, NULL, '\"LIVREUR\"', '2025-05-24 13:29:07', '2025-05-24 13:29:43'),
(4, 'Chedly', 'Hachicha', '<EMAIL>', '$2y$10$zPboC6/HYMlAzjEraobd/.18uwDLMsh5qYOqfgLitJBUlheok8RiO', NULL, NULL, '\"ADMIN\"', '2025-05-24 13:26:37', '2025-05-24 13:26:56');

-- --------------------------------------------------------

--
-- Structure de la table `wishlist`
--

DROP TABLE IF EXISTS `wishlist`;
CREATE TABLE IF NOT EXISTS `wishlist` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `product_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_product` (`user_id`,`product_id`),
  KEY `product_id` (`product_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
