/* Base Styles */
:root {
    --primary-color: #000;
    --secondary-color: #fff;
    --accent-color: #f5f5f5;
    --text-color: #333;
    --light-gray: #e0e0e0;
    --font-primary: 'Times New Roman', serif;
    --font-secondary: Georgia, serif;
    --max-width: 1200px;
    --spacing-sm: 10px;
    --spacing-md: 20px;
    --spacing-lg: 40px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--secondary-color);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: normal;
    margin-bottom: var(--spacing-md);
}

h2 {
    font-size: 28px;
    font-style: italic;
}

a {
    text-decoration: none;
    color: inherit;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

main {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

section {
    margin: var(--spacing-lg) 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    cursor: pointer;
    text-transform: lowercase;
    font-family: var(--font-secondary);
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #333;
}

.btn-secondary {
    display: inline-block;
    padding: 8px 16px;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
    cursor: pointer;
    text-transform: lowercase;
    font-family: var(--font-secondary);
    transition: all 0.3s;
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

/* Header Styles */
header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--light-gray);
    background-color: var(--secondary-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: var(--max-width);
    margin: 0 auto;
}

.logo {
    font-family: var(--font-secondary);
    font-size: 24px;
    font-style: italic;
}

.menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 20px;
    cursor: pointer;
}

.menu-toggle span {
    height: 2px;
    width: 100%;
    background-color: var(--primary-color);
}

.search-bar {
    display: flex;
    border-bottom: 1px solid var(--light-gray);
}

.search-bar input {
    border: none;
    padding: 5px;
    outline: none;
    font-family: var(--font-primary);
    background-color: transparent;
}

.search-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('../images/search-icon.svg');
    background-size: contain;
    background-repeat: no-repeat;
}

.user-actions {
    display: flex;
    gap: var(--spacing-md);
}

.user-actions a {
    font-family: var(--font-secondary);
    text-transform: lowercase;
    position: relative;
}

.cart-icon {
    position: relative;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary-color);
    color: var(--secondary-color);
    font-size: 12px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-nav {
    max-width: var(--max-width);
    margin: var(--spacing-md) auto 0;
}

.main-nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
}

.main-nav li {
    margin: 0 15px;
    position: relative;
}

.main-nav a {
    text-transform: lowercase;
    font-family: var(--font-secondary);
    position: relative;
    display: block;
    padding: 10px 0;
}

.main-nav a:after {
    content: '';
    position: absolute;
    width: 0;
    height: 1px;
    bottom: 8px;
    left: 0;
    background-color: var(--primary-color);
    transition: width 0.3s;
}

.main-nav a:hover:after {
    width: 100%;
}

/* Dropdown Styles */
.main-nav .dropdown {
    position: relative;
}

.main-nav .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--secondary-color);
    border: 1px solid var(--light-gray);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.main-nav .dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
}

.main-nav .dropdown-menu ul {
    flex-direction: column;
    padding: 10px 0;
    margin: 0;
}

.main-nav .dropdown-menu li {
    margin: 0;
    width: 100%;
}

.main-nav .dropdown-menu a {
    padding: 8px 20px;
    font-size: 14px;
    color: var(--text-color);
    border-bottom: none;
}

.main-nav .dropdown-menu a:after {
    display: none;
}

.main-nav .dropdown-menu a:hover {
    background-color: var(--accent-color);
}

/* Hero Section */
.hero {
    max-width: var(--max-width);
    margin: var(--spacing-lg) auto;
    text-align: center;
}

.hero h1 {
    font-size: 36px;
    margin-bottom: 30px;
    font-style: italic;
}

.hero-image {
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.hero-image img {
    width: 100%;
    transition: transform 0.5s ease;
}

.hero-image:hover img {
    transform: scale(1.02);
}

.brand-description {
    max-width: 800px;
    margin: 0 auto var(--spacing-lg);
    font-size: 16px;
    line-height: 1.8;
}

.brand-description p {
    margin-bottom: var(--spacing-md);
}

.cta-button {
    margin: var(--spacing-lg) 0;
}

/* Categories Section */
.categories {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin: var(--spacing-lg) 0;
}

.category {
    text-align: center;
    position: relative;
    overflow: hidden;
}

.category img {
    width: 100%;
    transition: transform 0.5s ease;
}

.category:hover img {
    transform: scale(1.05);
}

.category h3 {
    margin-top: var(--spacing-sm);
    font-style: italic;
}

/* Brand Philosophy */
.brand-philosophy {
    margin: var(--spacing-lg) 0;
    text-align: center;
}

.philosophy-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-md);
}

.philosophy-text {
    flex: 1;
    text-align: left;
    padding-right: var(--spacing-md);
}

.philosophy-image {
    flex: 1;
}

/* Testimonials */
.testimonials {
    margin: var(--spacing-lg) 0;
    text-align: center;
}

.testimonials-container {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-md);
    margin: var(--spacing-md) 0;
}

.testimonial {
    flex: 1;
    padding: var(--spacing-md);
    border: 1px solid var(--light-gray);
}

.testimonial p {
    font-style: italic;
    margin-bottom: var(--spacing-sm);
}

.author {
    font-weight: bold;
}

.recommendation-form {
    margin-top: var(--spacing-lg);
}

/* Shop Page */
.shop-page main {
    padding-top: 0;
}

.shop-banner {
    position: relative;
    width: 100%;
    height: 300px;
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.shop-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--secondary-color);
    font-size: 24px;
    font-style: italic;
    text-align: center;
    width: 100%;
    padding: 0 var(--spacing-md);
}

.sales-section, .perfume-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.sales-content, .perfume-content {
    flex: 1;
}

.sales-image, .perfume-image {
    flex: 1;
}

.seasonal-collection {
    margin: var(--spacing-lg) 0;
}

.collection-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: var(--spacing-md);
}

.collection-item {
    position: relative;
    overflow: hidden;
}

.collection-item img {
    width: 100%;
    transition: transform 0.5s ease;
}

.collection-item:hover img {
    transform: scale(1.05);
}

.item-details {
    padding: var(--spacing-sm) 0;
}

.item-details h3 {
    margin-bottom: 5px;
}

.item-details p {
    font-size: 14px;
    margin-bottom: 5px;
}

.price {
    font-weight: bold;
}

/* Product Filters Section */
.product-filters {
    margin: var(--spacing-lg) 0;
    padding: var(--spacing-lg) 0;
    border-bottom: 1px solid var(--light-gray);
}

.product-filters h2 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    position: relative;
    display: inline-block;
    width: 100%;
}

.product-filters h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 2px;
    background-color: var(--primary-color);
}

.filter-container {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    align-items: flex-end;
    flex-wrap: wrap;
    max-width: 800px;
    margin: 0 auto;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
    position: relative;
}

.filter-group label {
    font-family: var(--font-secondary);
    font-style: italic;
    font-size: 16px;
    margin-bottom: var(--spacing-sm);
    color: var(--text-color);
    font-weight: normal;
    text-transform: lowercase;
}

.filter-group select {
    padding: 12px 16px;
    border: 2px solid var(--light-gray);
    background-color: var(--secondary-color);
    font-family: var(--font-primary);
    font-size: 14px;
    color: var(--text-color);
    border-radius: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.filter-group select:hover {
    border-color: var(--primary-color);
}

.filter-group select option {
    padding: 8px;
    font-family: var(--font-primary);
    background-color: var(--secondary-color);
    color: var(--text-color);
}

/* Products Section */
.products-section {
    margin: var(--spacing-lg) 0;
}

.no-products {
    text-align: center;
    padding: var(--spacing-lg);
    color: #666;
    font-style: italic;
    font-size: 18px;
}

/* Enhanced Product Cards */
.product-card {
    border: 1px solid var(--light-gray);
    background-color: var(--secondary-color);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.product-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover img {
    transform: scale(1.05);
}

.product-card .product-details {
    padding: var(--spacing-md);
}

.product-card h3 {
    font-size: 18px;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    font-style: italic;
    font-weight: normal;
}

.product-card p {
    font-size: 14px;
    margin-bottom: var(--spacing-md);
    color: #666;
    line-height: 1.5;
}

.product-card .price {
    margin-bottom: var(--spacing-md);
    font-size: 16px;
}

.product-card .add-to-cart {
    width: calc(100% - var(--spacing-md) * 2);
    margin: 0 var(--spacing-md) var(--spacing-md);
    padding: 10px;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Sale Badge */
.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 2;
}

.product-card.sale {
    border-color: #dc3545;
}

.product-card.sale::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background-color: #dc3545;
    z-index: 1;
}

/* Product Page */
.product-container {
    display: flex;
    gap: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.product-images {
    flex: 1;
}

.product-details {
    flex: 1;
}

.product-price {
    font-size: 24px;
    margin-bottom: var(--spacing-md);
}

.product-description {
    margin-bottom: var(--spacing-md);
}

.product-actions {
    margin-top: var(--spacing-lg);
}

.complementary-products {
    margin: var(--spacing-lg) 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.product-card {
    border: 1px solid var(--light-gray);
    padding: var(--spacing-sm);
    text-align: center;
}

.product-card img {
    width: 100%;
    margin-bottom: var(--spacing-sm);
}

.product-card h3 {
    font-size: 16px;
    margin-bottom: 5px;
}

.product-card p {
    font-size: 14px;
    margin-bottom: 5px;
}

/* Footer */
footer {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    padding: var(--spacing-lg) var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.footer-container {
    max-width: var(--max-width);
    margin: 0 auto;
}

.social-section {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-sm);
}

.social-icon {
    width: 30px;
    height: 30px;
    background-color: var(--secondary-color);
    border-radius: 50%;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
}

.footer-column h4 {
    margin-bottom: var(--spacing-sm);
}

.footer-column ul {
    list-style: none;
}

.footer-column li {
    margin-bottom: 5px;
}

/* Messages */
.message {
    padding: 15px 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    font-weight: bold;
    transition: opacity 0.3s ease;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Profile Page Styles */
.profile-section {
    margin: var(--spacing-lg) 0;
}

.profile-container {
    max-width: 1000px;
    margin: 0 auto;
}

/* Profile Tab Navigation */
.profile-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--light-gray);
    padding-bottom: 10px;
    flex-wrap: wrap;
}

.profile-tab-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: transparent;
    border: 1px solid var(--light-gray);
    cursor: pointer;
    font-family: var(--font-secondary);
    font-size: 14px;
    transition: all 0.3s ease;
    border-radius: 4px;
    min-width: 160px;
    justify-content: center;
}

.profile-tab-btn:hover {
    background-color: var(--accent-color);
    border-color: var(--primary-color);
}

.profile-tab-btn.active {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    border-color: var(--primary-color);
}

.profile-tab-btn .tab-icon {
    font-size: 16px;
}

.profile-tab-btn .tab-text {
    font-weight: normal;
}

/* Profile Content */
.profile-content {
    min-height: 500px;
}

.profile-tab {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.profile-tab.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Section Headers */
.section-header {
    margin-bottom: var(--spacing-lg);
    text-align: center;
    border-bottom: 1px solid var(--light-gray);
    padding-bottom: var(--spacing-md);
}

.section-header h2 {
    margin-bottom: 10px;
}

.section-description {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* Form Styles */
.profile-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    font-family: var(--font-secondary);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--light-gray);
    font-family: var(--font-primary);
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Password Change Section */
.password-change {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--light-gray);
}

.password-change h3 {
    margin-bottom: var(--spacing-md);
    font-size: 20px;
}

/* Orders Section */
.orders-list {
    max-width: 800px;
    margin: 0 auto;
}

.order-item {
    border: 1px solid var(--light-gray);
    margin-bottom: var(--spacing-md);
    border-radius: 4px;
    overflow: hidden;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background-color: var(--accent-color);
    border-bottom: 1px solid var(--light-gray);
}

.order-number {
    font-weight: bold;
    font-family: var(--font-secondary);
}

.order-date {
    color: #666;
    font-size: 14px;
}

.order-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-delivered {
    background-color: #d4edda;
    color: #155724;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-processing {
    background-color: #cce5ff;
    color: #004085;
}

.order-details {
    padding: var(--spacing-md);
}

.order-products {
    margin-bottom: var(--spacing-md);
}

.order-product {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid #f0f0f0;
}

.order-product:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.order-product img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
}

.product-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
}

.product-info p {
    margin: 2px 0;
    font-size: 12px;
    color: #666;
}

.order-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid #f0f0f0;
}

.order-summary p {
    font-weight: bold;
    margin: 0;
}

/* Addresses Section */
.addresses-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
    max-width: 900px;
    margin: 0 auto;
}

.address-card {
    border: 1px solid var(--light-gray);
    padding: var(--spacing-md);
    border-radius: 4px;
    background-color: #fff;
}

.address-type {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: var(--spacing-sm);
}

.address-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.address-badge.primary {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

.address-type h3 {
    margin: 0;
    font-size: 16px;
}

.address-details p {
    margin: 5px 0;
    font-size: 14px;
    line-height: 1.4;
}

.address-actions {
    margin-top: var(--spacing-md);
    display: flex;
    gap: 10px;
}

.btn-danger {
    border-color: #dc3545;
    color: #dc3545;
}

.btn-danger:hover {
    background-color: #dc3545;
    color: white;
}

.add-address-card {
    border: 2px dashed var(--light-gray);
    padding: var(--spacing-lg);
    border-radius: 4px;
    text-align: center;
    background-color: #fafafa;
}

.add-address-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.add-icon {
    font-size: 24px;
    color: #999;
}

.add-address-content h3 {
    margin: 0;
    font-size: 16px;
    color: #666;
}

.add-address-content p {
    margin: 0;
    font-size: 14px;
    color: #999;
}

/* Wishlist Section */
.wishlist-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
    max-width: 900px;
    margin: 0 auto;
}

.wishlist-product {
    border: 1px solid var(--light-gray);
    border-radius: 4px;
    overflow: hidden;
    background-color: #fff;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.wishlist-product:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.remove-wishlist {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.remove-wishlist:hover {
    background: #ff4757;
    color: white;
    transform: scale(1.1);
}

.wishlist-product .product-info {
    padding: var(--spacing-md);
}

.wishlist-product .product-info h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-family: var(--font-secondary);
}

.wishlist-product .product-info p {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.wishlist-product .price {
    font-weight: bold;
    font-size: 18px;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.sale-price {
    color: #dc3545;
    font-weight: bold;
}

.original-price {
    text-decoration: line-through;
    color: #999;
    font-size: 14px;
    margin-left: 8px;
}

.discount {
    background-color: #dc3545;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin-left: 8px;
}

.form-group small {
    color: #666;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.product-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.product-status.in-stock {
    background-color: #d4edda;
    color: #155724;
}

.product-status.out-of-stock {
    background-color: #f8d7da;
    color: #721c24;
}

.wishlist-actions {
    padding: var(--spacing-md);
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 10px;
}

.wishlist-actions .btn,
.wishlist-actions .btn-secondary {
    flex: 1;
    text-align: center;
    padding: 8px 12px;
    font-size: 13px;
}

/* Settings Section */
.settings-sections {
    max-width: 700px;
    margin: 0 auto;
}

.settings-section {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border: 1px solid var(--light-gray);
    border-radius: 4px;
    background-color: #fff;
}

.settings-section h3 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: 18px;
    font-family: var(--font-secondary);
    color: var(--primary-color);
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
}

.toggle-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid #f0f0f0;
}

.toggle-group:last-child {
    border-bottom: none;
}

.toggle-info {
    flex: 1;
}

.toggle-info label {
    font-weight: bold;
    margin-bottom: 5px;
    font-family: var(--font-secondary);
}

.toggle-info p {
    margin: 0;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.toggle-switch {
    position: relative;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: var(--primary-color);
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px);
}

.settings-actions {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--light-gray);
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: var(--spacing-lg);
    max-width: 400px;
    margin: 0 auto;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: var(--spacing-sm);
    color: #666;
    font-size: 20px;
}

.empty-state p {
    margin-bottom: var(--spacing-md);
    color: #999;
    line-height: 1.6;
}

/* About Page Styles */
.about-page main {
    padding-top: 0;
}

.about-hero {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.about-hero h1 {
    font-size: 36px;
    margin-bottom: var(--spacing-lg);
    font-style: italic;
    color: var(--primary-color);
}

.about-image {
    margin-bottom: var(--spacing-lg);
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.about-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.about-image:hover img {
    transform: scale(1.02);
}

.about-description {
    max-width: 800px;
    margin: 0 auto;
    font-size: 16px;
    line-height: 1.8;
    color: var(--text-color);
}

.about-description p {
    margin-bottom: var(--spacing-md);
}

/* Values Section */
.our-values {
    margin: var(--spacing-lg) 0;
}

.our-values h2 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-md);
}

.value-item {
    text-align: center;
    padding: var(--spacing-lg);
    border: 1px solid var(--light-gray);
    border-radius: 8px;
    background-color: var(--secondary-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.value-item h3 {
    font-size: 20px;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    font-style: italic;
}

.value-item p {
    line-height: 1.6;
    color: #666;
}

/* Process Section */
.our-process {
    margin: var(--spacing-lg) 0;
}

.our-process h2 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.process-content {
    display: flex;
    gap: var(--spacing-lg);
    align-items: flex-start;
}

.process-image {
    flex: 1;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.process-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.process-image:hover img {
    transform: scale(1.02);
}

.process-steps {
    flex: 1;
}

.step {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border-left: 3px solid var(--primary-color);
    background-color: var(--accent-color);
    border-radius: 0 8px 8px 0;
}

.step h3 {
    font-size: 18px;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    font-style: italic;
}

.step p {
    line-height: 1.6;
    color: #666;
    margin: 0;
}

/* Team Section */
.team {
    margin: var(--spacing-lg) 0;
    text-align: center;
}

.team h2 {
    margin-bottom: var(--spacing-lg);
    position: relative;
    display: inline-block;
}

.team h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background-color: var(--primary-color);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.team-member {
    background-color: var(--secondary-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
}

.team-member:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.team-member img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.team-member:hover img {
    transform: scale(1.05);
}

.team-member h3 {
    font-size: 20px;
    margin: var(--spacing-md) var(--spacing-md) var(--spacing-sm);
    color: var(--primary-color);
    font-style: italic;
    font-weight: normal;
}

.team-member p {
    margin: 0 var(--spacing-md) var(--spacing-md);
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 300;
}

.team-member::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #333);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.team-member:hover::before {
    opacity: 1;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .categories,
    .collection-grid,
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .philosophy-content,
    .sales-section,
    .perfume-section,
    .product-container,
    .process-content {
        flex-direction: column;
    }

    /* About page responsive */
    .values-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: var(--spacing-md);
    }

    .team-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }

    .about-image img {
        height: 300px;
    }

    .team-member img {
        height: 250px;
    }

    /* Shop page responsive */
    .filter-container {
        gap: var(--spacing-md);
        justify-content: space-around;
    }

    .filter-group {
        min-width: 180px;
    }

    .product-card img {
        height: 220px;
    }

    /* Profile responsive */
    .profile-tabs {
        justify-content: center;
    }

    .profile-tab-btn {
        min-width: 140px;
        font-size: 13px;
        padding: 10px 15px;
    }

    .addresses-container,
    .wishlist-items {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .order-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .order-summary {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .settings-actions {
        flex-direction: column;
        align-items: center;
    }

    .settings-actions .btn,
    .settings-actions .btn-secondary {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .menu-toggle {
        display: flex;
    }

    .main-nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--secondary-color);
        border-top: 1px solid var(--light-gray);
        z-index: 1000;
    }

    .main-nav.active {
        display: block;
    }

    .main-nav ul {
        flex-direction: column;
        padding: 0;
    }

    .main-nav li {
        margin: 0;
        border-bottom: 1px solid var(--light-gray);
    }

    .main-nav a {
        padding: 15px 20px;
        display: block;
    }

    .main-nav .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        border: none;
        box-shadow: none;
        background-color: var(--accent-color);
    }

    .main-nav .dropdown-menu ul {
        padding: 0;
    }

    .main-nav .dropdown-menu a {
        padding: 10px 40px;
        font-size: 13px;
    }

    .categories,
    .collection-grid,
    .products-grid,
    .testimonials-container,
    .values-grid,
    .team-grid {
        grid-template-columns: 1fr;
    }

    /* About page mobile styles */
    .about-hero h1 {
        font-size: 28px;
    }

    .about-image img {
        height: 250px;
    }

    .team-member img {
        height: 200px;
    }

    .value-item {
        padding: var(--spacing-md);
    }

    .step {
        margin-bottom: var(--spacing-md);
        padding: var(--spacing-sm);
    }

    /* Shop page mobile styles */
    .filter-container {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: center;
    }

    .filter-group {
        min-width: 250px;
        width: 100%;
        max-width: 300px;
    }

    .filter-group select {
        padding: 14px 16px;
        font-size: 16px;
    }

    .product-card img {
        height: 200px;
    }

    .product-card h3 {
        font-size: 16px;
    }

    .product-card .add-to-cart {
        font-size: 12px;
        padding: 8px;
    }

    /* Profile mobile styles */
    .profile-tabs {
        flex-direction: column;
        gap: 5px;
    }

    .profile-tab-btn {
        min-width: auto;
        width: 100%;
        justify-content: flex-start;
        padding: 15px 20px;
        font-size: 14px;
    }

    .profile-tab-btn .tab-text {
        display: block;
    }

    .addresses-container,
    .wishlist-items {
        grid-template-columns: 1fr;
    }

    .address-actions,
    .wishlist-actions {
        flex-direction: column;
    }

    .toggle-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .toggle-switch {
        align-self: flex-end;
    }

    .order-product {
        flex-direction: column;
        text-align: center;
    }

    .order-product img {
        width: 80px;
        height: 80px;
        align-self: center;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: var(--secondary-color);
    margin: 5% auto;
    padding: 0;
    border: 1px solid var(--light-gray);
    width: 90%;
    max-width: 500px;
    border-radius: 4px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--light-gray);
}

.modal-header h3 {
    margin: 0;
    font-family: var(--font-secondary);
    font-style: italic;
}

.close {
    color: var(--text-color);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover,
.close:focus {
    color: var(--primary-color);
    text-decoration: none;
}

.modal form {
    padding: var(--spacing-md);
}

.form-row {
    display: flex;
    gap: var(--spacing-md);
}

.form-row .form-group {
    flex: 1;
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-family: var(--font-secondary);
    font-weight: normal;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--light-gray);
    font-family: var(--font-primary);
    font-size: 14px;
    background-color: var(--secondary-color);
    resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox-group label {
    margin-bottom: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--light-gray);
    margin-top: var(--spacing-md);
}

/* Modal responsive styles */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .modal-actions {
        flex-direction: column;
    }

    .modal-actions button {
        width: 100%;
    }
}

/* Quick View Modal Styles */
.quick-view-content {
    max-width: 800px;
    width: 95%;
}

.quick-view-body {
    display: flex;
    gap: var(--spacing-lg);
    padding: var(--spacing-md);
}

.quick-view-image {
    flex: 1;
}

.quick-view-image img {
    width: 100%;
    height: auto;
    border-radius: 4px;
}

.quick-view-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.quick-view-details h2 {
    margin: 0;
    font-family: var(--font-secondary);
    font-style: italic;
}

.quick-view-details .price {
    font-size: 18px;
    font-weight: bold;
}

.quick-view-details .sale-price {
    color: #e74c3c;
    margin-right: 10px;
}

.quick-view-details .original-price {
    text-decoration: line-through;
    color: #999;
    font-size: 16px;
}

.quick-view-details .description {
    color: #666;
    line-height: 1.6;
}

.stock-status {
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}

.stock-status.in-stock {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.stock-status.out-of-stock {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.quick-view-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: auto;
}

.quick-view-actions .btn,
.quick-view-actions .btn-secondary {
    flex: 1;
    text-align: center;
    padding: 12px 20px;
}

/* Quick view responsive styles */
@media (max-width: 768px) {
    .quick-view-body {
        flex-direction: column;
    }

    .quick-view-actions {
        flex-direction: column;
    }

    .quick-view-actions .btn,
    .quick-view-actions .btn-secondary {
        width: 100%;
    }
}

/* Checkout Success Styles */
.checkout-success {
    text-align: center;
    max-width: 600px;
    margin: var(--spacing-lg) auto;
    padding: var(--spacing-lg);
    border: 2px solid #28a745;
    border-radius: 8px;
    background-color: #f8fff9;
}

.checkout-success h1 {
    color: #28a745;
    margin-bottom: var(--spacing-lg);
    font-family: var(--font-secondary);
    font-style: italic;
}

.success-details {
    margin-bottom: var(--spacing-lg);
}

.success-details p {
    margin-bottom: var(--spacing-sm);
    line-height: 1.6;
}

.success-details p:first-child {
    font-size: 18px;
    color: #28a745;
    background-color: #d4edda;
    padding: var(--spacing-sm);
    border-radius: 4px;
    border: 1px solid #c3e6cb;
}

.success-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.success-actions .btn,
.success-actions .btn-secondary {
    min-width: 180px;
}

/* Checkout success responsive styles */
@media (max-width: 768px) {
    .checkout-success {
        margin: var(--spacing-md);
        padding: var(--spacing-md);
    }

    .success-actions {
        flex-direction: column;
        align-items: center;
    }

    .success-actions .btn,
    .success-actions .btn-secondary {
        width: 100%;
        max-width: 300px;
    }
}
