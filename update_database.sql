-- Update script to modify existing database for blob image support and fix addresses
-- Run this if you already have the database created

USE NuitBlanche;

-- Modify the ImageFile column to be LONGBLOB and allow NULL
ALTER TABLE products MODIFY COLUMN ImageFile LONGBLOB;

-- Update existing empty string values to NULL for better handling
UPDATE products SET ImageFile = NULL WHERE ImageFile = '';

-- Create addresses table for proper order management
CREATE TABLE IF NOT EXISTS `addresses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `type` enum('shipping','billing') DEFAULT 'shipping',
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `address_line1` varchar(255) NOT NULL,
  `address_line2` varchar(255) DEFAULT NULL,
  `city` varchar(100) NOT NULL,
  `postal_code` varchar(20) NOT NULL,
  `country` varchar(100) DEFAULT 'France',
  `phone` varchar(20) DEFAULT NULL,
  `is_default` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Insert default addresses for existing orders (using user information)
INSERT INTO addresses (user_id, type, first_name, last_name, address_line1, city, postal_code, phone, is_default)
SELECT DISTINCT u.id, 'shipping', u.first_name, u.last_name,
       'Adresse non spécifiée', 'Ville non spécifiée', '00000', u.phone, 1
FROM users u
WHERE u.id IN (SELECT DISTINCT user_id FROM orders)
AND NOT EXISTS (SELECT 1 FROM addresses a WHERE a.user_id = u.id);

-- Verify the changes
SELECT id, name, image,
       CASE
           WHEN ImageFile IS NULL THEN 'NULL'
           WHEN LENGTH(ImageFile) = 0 THEN 'EMPTY'
           ELSE CONCAT('BLOB (', LENGTH(ImageFile), ' bytes)')
       END as ImageFile_status
FROM products;
